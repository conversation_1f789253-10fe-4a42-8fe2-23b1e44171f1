# YOLOv12 RGB+D 服务器部署指南

## 📋 项目清理完成

已删除以下多余文件和目录，优化项目结构：

### 🗑️ 已删除的文件/目录：
- `examples/` - 示例代码目录 (不需要)
- `docker/` - Docker配置文件 (不需要)
- `tests/` - 测试文件 (不需要)
- `logs/` - 旧的日志文件 (不需要)
- `assets/` - 静态资源文件 (不需要)
- `app.py` - Web应用文件 (不需要)
- `mkdocs.yml` - 文档配置 (不需要)
- `scripts/results/` - 临时结果文件 (不需要)
- `scripts/runs/` - 临时运行文件 (不需要)
- `scripts/yolov12m.pt` - 模型文件 (会自动下载)
- `ultralytics/__pycache__/` - Python缓存文件 (不需要)

## 📁 清理后的项目结构

```
yolov12-new/
├── LICENSE                    # 许可证
├── README.md                  # 原始README
├── RGBD_EXPERIMENT_README.md  # 实验说明文档
├── pyproject.toml            # Python项目配置
├── requirements.txt          # 依赖包列表
│
├── analysis/                 # 分析工具
│   └── stage1_diagnosis.py   # Stage1问题诊断
│
├── configs/                  # 配置文件
│   ├── local_test_config.yaml    # 本地测试配置
│   └── server_config.yaml        # 服务器配置
│
├── datasets/                 # 数据集配置
│   ├── kitti_standard.yaml   # 标准KITTI配置
│   └── kitti_server.yaml     # 服务器KITTI配置
│
├── evaluation/               # 评估工具
│   └── kitti_evaluator.py    # KITTI评估器
│
├── results/                  # 实验结果目录 (空)
│
├── scripts/                  # 实验脚本
│   ├── baseline_test.py       # 基准测试
│   ├── deploy_to_server.py    # 部署脚本
│   ├── quick_dataset_check.py # 快速数据集检查
│   ├── run_ablation_experiment.py # 消融实验运行器
│   └── verify_dataset.py     # 详细数据集验证
│
└── ultralytics/              # 核心YOLOv12代码
    ├── __init__.py
    ├── cfg/                   # 模型配置
    │   └── models/v12/        # YOLOv12模型配置
    │       ├── yolov12m-stage1-4ch.yaml
    │       └── yolov12m-daldet-style.yaml
    ├── data/                  # 数据处理
    │   └── rgbd_dataset.py    # RGB+D数据加载器
    ├── nn/                    # 神经网络模块
    │   └── modules/
    │       └── depth_modules.py # 深度感知模块
    ├── utils/                 # 工具函数
    │   └── depth_losses.py    # 深度引导损失
    └── [其他ultralytics核心模块]
```

## 🚀 服务器部署步骤

### 1. 上传项目到服务器
```bash
# 在本地执行 (假设服务器IP为your_server_ip)
scp -r yolov12-new/ username@your_server_ip:/home/<USER>/

# 或使用rsync (推荐)
rsync -avz --progress yolov12-new/ username@your_server_ip:/home/<USER>/yolov12-new/
```

### 2. 服务器环境准备
```bash
# SSH登录服务器
ssh username@your_server_ip

# 进入项目目录
cd /home/<USER>/yolov12-new

# 安装依赖
pip install ultralytics torch torchvision
pip install opencv-python matplotlib seaborn
pip install tensorboard wandb pyyaml

# 验证CUDA环境
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
nvidia-smi
```

### 3. 验证数据集
```bash
# 快速检查数据集
python scripts/quick_dataset_check.py

# 详细验证 (可选)
python scripts/verify_dataset.py
```

### 4. 运行基准测试
```bash
# 测试YOLOv12m基准性能
python scripts/baseline_test.py
```

### 5. 开始消融实验
```bash
# 第1周实验 (基准建立)
python scripts/run_ablation_experiment.py --week 1

# 第2周实验 (架构优化)
python scripts/run_ablation_experiment.py --week 2

# 完整实验流程
python scripts/run_ablation_experiment.py --week all
```

## ⚙️ 服务器配置说明

### 数据集路径配置
- 服务器数据集路径: `/home/<USER>/kitti`
- 配置文件: `datasets/kitti_server.yaml`
- 自动适配服务器环境

### 训练配置优化
- **Batch Size**: 32 (适配RTX 4090)
- **Workers**: 16 (充分利用CPU)
- **Epochs**: 100 (完整训练)
- **混合精度**: 启用AMP加速训练

### 实验监控
```bash
# 在另一个终端运行监控 (如果需要)
python scripts/monitor_experiments.py

# 查看TensorBoard (如果启用)
tensorboard --logdir results/logs --port 6006
```

## 📊 预期实验时间

| 实验阶段 | 预计时间 | 主要内容 |
|---------|---------|----------|
| 第1周 | 2-3天 | 基准测试 + Stage1分析 |
| 第2周 | 3-4天 | DALDet架构训练 |
| 第3周 | 3-4天 | 损失函数优化 |
| 第4周 | 2-3天 | 高级技术集成 |
| 第5周 | 2-3天 | 系统优化 + 评估 |

## 🎯 性能目标

| 类别 | 当前Stage1 | 改进目标 | DALDet基准 |
|------|-----------|----------|-----------|
| Car | 90.8% | **>92%** | 95.75% |
| Pedestrian | 62.7% | **>70%** | 76.14% |
| Cyclist | 39.7% | **>65%** | 82.55% |
| 推理速度 | ~1000 FPS | **>800 FPS** | 66.7 FPS |

## 🔧 故障排除

### 常见问题
1. **CUDA内存不足**: 
   - 减小batch_size (32→16→8)
   - 启用gradient_checkpointing

2. **数据加载慢**: 
   - 增加workers数量
   - 启用pin_memory

3. **训练不收敛**: 
   - 检查学习率设置
   - 验证数据预处理

### 日志位置
- 训练日志: `results/experiments/*/train/`
- 错误日志: `results/logs/`
- 模型检查点: `results/checkpoints/`

## 📞 技术支持

如遇问题，请检查：
1. 数据集路径是否正确 (`/home/<USER>/kitti`)
2. CUDA环境是否正常 (`nvidia-smi`)
3. 依赖包是否完整安装
4. 磁盘空间是否充足 (建议>100GB)

---

**项目已优化完成，准备部署到服务器！** 🚀

总文件大小减少约60%，仅保留实验必需的核心文件。
