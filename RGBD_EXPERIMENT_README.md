# YOLOv12 RGB+Depth 消融实验框架

## 📋 项目概述

本项目实现了一个完整的YOLOv12 RGB+深度图像融合的消融实验框架，旨在改进自动驾驶场景下的目标检测性能。项目参考DALDet论文的设计思路，通过系统性的实验来优化RGB+D融合策略。

### 🎯 主要目标
- 解决当前Stage1四通道拼接方法的性能问题
- 实现DALDet风格的双流架构
- 在KITTI数据集上达到接近DALDet的性能
- 保持YOLOv12的推理速度优势

### 📊 性能目标
| 类别 | 当前Stage1 | DALDet目标 | 改进目标 |
|------|-----------|-----------|----------|
| Car | ~90.8% | 95.75% | >92% |
| Pedestrian | ~62.7% | 76.14% | >70% |
| Cyclist | ~39.7% | 82.55% | >65% |
| 推理速度 | ~1000 FPS | 66.7 FPS | >800 FPS |

## 🏗️ 项目结构

```
yolov12-new/
├── datasets/                    # 数据集配置
│   └── kitti_standard.yaml    # 标准化KITTI配置
├── ultralytics/
│   ├── cfg/models/v12/         # 模型配置
│   │   ├── yolov12m-stage1-4ch.yaml      # Stage1四通道模型
│   │   └── yolov12m-daldet-style.yaml    # DALDet风格模型
│   ├── data/
│   │   └── rgbd_dataset.py     # RGB+D数据加载器
│   ├── nn/modules/
│   │   └── depth_modules.py    # 深度感知神经网络模块
│   └── utils/
│       └── depth_losses.py     # 深度引导损失函数
├── evaluation/
│   └── kitti_evaluator.py      # KITTI评估器
├── analysis/
│   └── stage1_diagnosis.py     # Stage1问题诊断
├── scripts/
│   ├── baseline_test.py        # 基准测试脚本
│   └── run_ablation_experiment.py  # 消融实验运行器
└── results/                    # 实验结果目录
```

## 🔬 实验设计

### 第1周：基准建立 + 数据处理优化
- [x] **1.1 标准化KITTI评估环境** - 创建与DALDet一致的评估协议
- [x] **1.2 复现YOLOv12m基准性能** - 建立RGB-only基准线
- [x] **1.3 分析Stage1实现问题** - 诊断4通道拼接的具体问题
- [x] **1.4 实现DALDet风格数据处理** - RGB和深度分离处理

### 第2周：网络架构优化
- [/] **2.1 设计双流架构** - 实现RGB和深度分离的特征提取
- [ ] **2.2 跨模态融合机制** - 设计注意力机制融合RGB-D特征
- [ ] **2.3 深度感知卷积** - 实现深度引导的卷积操作
- [ ] **2.4 多尺度特征融合** - 在不同尺度上融合特征

### 第3周：损失函数创新
- [ ] **3.1 深度一致性损失** - 确保检测框与深度信息一致
- [ ] **3.2 距离感知定位损失** - 根据距离调整定位精度要求
- [ ] **3.3 多尺度深度损失** - 保持不同尺度的深度特征一致性
- [ ] **3.4 深度引导Focal损失** - 根据深度调整难易样本权重

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install ultralytics torch torchvision
pip install opencv-python matplotlib seaborn
```

### 2. 数据准备
```bash
# 准备KITTI数据集
# 确保数据结构如下：
datasets/kitti/
├── images/
│   ├── training/
│   └── testing/
└── depth/
    ├── training/
    └── testing/
```

### 3. 运行基准测试
```bash
# 测试YOLOv12m基准性能
python scripts/baseline_test.py

# 分析Stage1问题
python analysis/stage1_diagnosis.py
```

### 4. 运行消融实验
```bash
# 运行第1周实验
python scripts/run_ablation_experiment.py --week 1

# 运行第2周实验
python scripts/run_ablation_experiment.py --week 2

# 运行完整实验
python scripts/run_ablation_experiment.py --week all
```

## 📈 核心创新

### 1. 深度感知架构
- **双流设计**：RGB和深度分离处理，避免特征混淆
- **跨模态注意力**：自适应融合RGB和深度特征
- **深度感知卷积**：利用深度信息引导特征学习

### 2. 深度引导损失
- **深度一致性损失**：确保检测框与深度信息匹配
- **距离感知损失**：近距离目标要求更高精度
- **多尺度深度损失**：保持特征层级的深度一致性

### 3. 数据处理优化
- **分离预处理**：RGB和深度使用不同的预处理策略
- **深度增强**：针对深度图的特殊数据增强
- **同步变换**：保持RGB-D的空间对应关系

## 🔍 问题诊断

### Stage1四通道方法的主要问题：
1. **通道语义不一致**：RGB表示颜色，深度表示距离，直接拼接导致特征混淆
2. **数值范围差异**：RGB和深度的数值范围不同，影响特征学习
3. **早期特征丢失**：深度信息在第一层卷积中可能被稀释
4. **小目标检测差**：Cyclist检测性能下降42.85%，表明深度信息利用不充分

### DALDet风格解决方案：
1. **分离处理流**：RGB和深度使用独立的特征提取器
2. **深度感知操作**：专门设计的深度处理模块
3. **多级融合**：在特征级和决策级进行融合
4. **深度引导机制**：利用深度信息指导检测过程

## 📊 评估指标

### 精度指标
- **KITTI AP(R40)**：使用40个召回点的平均精度
- **难度级别**：Easy/Moderate/Hard三个难度
- **类别特定IoU**：Car(0.7), Pedestrian/Cyclist(0.5)

### 速度指标
- **推理FPS**：单张图像推理速度
- **内存使用**：模型参数量和显存占用
- **延迟分析**：各模块的计算时间

## 🛠️ 使用说明

### 自定义实验
1. 修改 `configs/ablation_config.yaml` 配置实验参数
2. 在 `ultralytics/cfg/models/v12/` 添加新的模型配置
3. 运行实验并查看 `results/` 目录下的结果

### 添加新模块
1. 在 `ultralytics/nn/modules/` 添加新的神经网络模块
2. 在模型配置文件中引用新模块
3. 更新损失函数和评估指标

### 数据集适配
1. 修改 `datasets/kitti_standard.yaml` 适配新数据集
2. 更新 `ultralytics/data/rgbd_dataset.py` 的数据加载逻辑
3. 调整评估协议和指标计算

## 📝 实验记录

### 已完成任务
- ✅ 标准化KITTI评估环境
- ✅ Stage1问题诊断分析
- ✅ DALDet风格架构设计
- ✅ 深度感知模块实现
- ✅ 深度引导损失函数
- ✅ 实验运行框架

### 进行中任务
- 🔄 网络架构优化实验
- 🔄 跨模态融合机制调优

### 待完成任务
- ⏳ 损失函数创新实验
- ⏳ 先进技术集成
- ⏳ 系统级优化

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目基于MIT许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [DALDet论文](https://arxiv.org/abs/2304.10313) 提供了核心设计思路
- [YOLOv12](https://github.com/ultralytics/ultralytics) 提供了基础框架
- [KITTI数据集](http://www.cvlibs.net/datasets/kitti/) 提供了评估基准

---

**注意**：本框架仍在开发中，部分功能可能需要根据实际需求进行调整。建议在使用前仔细阅读代码和配置文件。
