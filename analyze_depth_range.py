import numpy as np
import os
from pathlib import Path
from PIL import Image

def analyze_depth_image(image_path):
    """分析单张深度图的数据范围"""
    # 读取深度图
    try:
        with Image.open(str(image_path)) as img:
            depth = np.array(img)
    except Exception as e:
        print(f"  ❌ 读取错误: {e}")
        return None
    
    # 统计信息
    stats = {
        'filename': image_path.name,
        'shape': depth.shape,
        'dtype': depth.dtype,
        'min_value': np.min(depth),
        'max_value': np.max(depth),
        'mean_value': np.mean(depth),
        'std_value': np.std(depth),
        'non_zero_count': np.count_nonzero(depth),
        'total_pixels': depth.size,
        'non_zero_ratio': np.count_nonzero(depth) / depth.size
    }
    
    # 计算一些百分位数
    non_zero_values = depth[depth > 0]
    if len(non_zero_values) > 0:
        stats['non_zero_min'] = np.min(non_zero_values)
        stats['non_zero_max'] = np.max(non_zero_values)
        stats['non_zero_mean'] = np.mean(non_zero_values)
        stats['percentile_25'] = np.percentile(non_zero_values, 25)
        stats['percentile_50'] = np.percentile(non_zero_values, 50)
        stats['percentile_75'] = np.percentile(non_zero_values, 75)
        stats['percentile_95'] = np.percentile(non_zero_values, 95)
    
    return stats

def main():
    depth_dir = Path("D:/Program_Files/Pycharm_2024.1.7/Pycharm_Projects/dataset/kitti/depths/train_set")
    
    # 获取前10张深度图
    depth_files = sorted(list(depth_dir.glob("*.png")))[:10]
    
    print("=" * 80)
    print("KITTI 深度图数据范围分析")
    print("=" * 80)
    
    all_stats = []
    
    for i, depth_file in enumerate(depth_files):
        print(f"\n[{i+1}/10] 分析: {depth_file.name}")
        stats = analyze_depth_image(depth_file)
        
        if stats is None:
            print(f"  ❌ 无法读取文件")
            continue
            
        all_stats.append(stats)
        
        print(f"  📐 图像尺寸: {stats['shape']}")
        print(f"  🔢 数据类型: {stats['dtype']}")
        print(f"  📊 数值范围: [{stats['min_value']}, {stats['max_value']}]")
        print(f"  📈 平均值: {stats['mean_value']:.2f}")
        print(f"  📉 标准差: {stats['std_value']:.2f}")
        print(f"  🎯 非零像素: {stats['non_zero_count']}/{stats['total_pixels']} ({stats['non_zero_ratio']*100:.1f}%)")
        
        if 'non_zero_min' in stats:
            print(f"  🔍 非零范围: [{stats['non_zero_min']}, {stats['non_zero_max']}]")
            print(f"  📊 非零均值: {stats['non_zero_mean']:.2f}")
            print(f"  📈 百分位数: P25={stats['percentile_25']:.0f}, P50={stats['percentile_50']:.0f}, P75={stats['percentile_75']:.0f}, P95={stats['percentile_95']:.0f}")
    
    # 汇总统计
    if all_stats:
        print("\n" + "=" * 80)
        print("汇总统计")
        print("=" * 80)
        
        all_mins = [s['min_value'] for s in all_stats]
        all_maxs = [s['max_value'] for s in all_stats]
        all_means = [s['mean_value'] for s in all_stats]
        all_non_zero_ratios = [s['non_zero_ratio'] for s in all_stats]
        
        print(f"📊 全局最小值: {min(all_mins)}")
        print(f"📊 全局最大值: {max(all_maxs)}")
        print(f"📊 平均值范围: [{min(all_means):.2f}, {max(all_means):.2f}]")
        print(f"📊 非零像素比例: [{min(all_non_zero_ratios)*100:.1f}%, {max(all_non_zero_ratios)*100:.1f}%]")
        
        # 分析数据编码方式
        print("\n" + "=" * 80)
        print("数据编码分析")
        print("=" * 80)
        
        max_val = max(all_maxs)
        if max_val <= 255:
            print("🔍 数据范围 [0, 255] - 可能是8位编码")
        elif max_val <= 65535:
            print("🔍 数据范围 [0, 65535] - 可能是16位编码")
            print(f"🔍 如果是IGEV-plusplus输出，原始视差范围约为 [0, {max_val/256:.1f}]")
        
        print(f"🔍 如果需要归一化到[0,1]，除以 {max_val}")

if __name__ == "__main__":
    main()
