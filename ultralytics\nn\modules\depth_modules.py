"""
深度感知神经网络模块
实现DALDet风格的深度处理和跨模态融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import math


class DepthAwareConv(nn.Module):
    """
    深度感知卷积模块
    参考DALDet论文中的深度感知操作
    """
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 stride: int = 1, padding: int = 1, dilation: int = 1):
        """
        初始化深度感知卷积
        
        Args:
            in_channels: 输入通道数
            out_channels: 输出通道数
            kernel_size: 卷积核大小
            stride: 步长
            padding: 填充
            dilation: 膨胀率
        """
        super().__init__()
        
        # 标准卷积
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, 
                             stride, padding, dilation, bias=False)
        
        # 深度引导的权重调制
        self.depth_modulation = nn.Sequential(
            nn.Conv2d(1, out_channels // 4, 3, 1, 1),  # 深度特征提取
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels // 4, out_channels, 1),  # 权重生成
            nn.Sigmoid()
        )
        
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, rgb_feat: torch.Tensor, depth_feat: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            rgb_feat: RGB特征 [B, C, H, W]
            depth_feat: 深度特征 [B, 1, H, W]
            
        Returns:
            融合后的特征
        """
        # 标准卷积处理RGB特征
        rgb_out = self.conv(rgb_feat)
        
        # 深度引导的权重调制
        if depth_feat.shape[2:] != rgb_out.shape[2:]:
            depth_feat = F.interpolate(depth_feat, size=rgb_out.shape[2:], 
                                     mode='bilinear', align_corners=False)
        
        depth_weights = self.depth_modulation(depth_feat)
        
        # 应用深度权重
        modulated_feat = rgb_out * depth_weights
        
        return self.act(self.bn(modulated_feat))


class CrossModalAttention(nn.Module):
    """
    跨模态注意力机制
    实现RGB和深度特征的自适应融合
    """
    
    def __init__(self, rgb_channels: int, depth_channels: int, 
                 reduction: int = 16):
        """
        初始化跨模态注意力
        
        Args:
            rgb_channels: RGB特征通道数
            depth_channels: 深度特征通道数
            reduction: 通道缩减比例
        """
        super().__init__()
        
        self.rgb_channels = rgb_channels
        self.depth_channels = depth_channels
        total_channels = rgb_channels + depth_channels
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, 7, padding=3, bias=False),
            nn.Sigmoid()
        )
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(total_channels, total_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(total_channels // reduction, total_channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 特征融合
        self.fusion_conv = nn.Conv2d(total_channels, rgb_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(rgb_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, rgb_feat: torch.Tensor, depth_feat: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            rgb_feat: RGB特征 [B, C_rgb, H, W]
            depth_feat: 深度特征 [B, C_depth, H, W]
            
        Returns:
            融合后的特征 [B, C_rgb, H, W]
        """
        B, _, H, W = rgb_feat.shape
        
        # 确保空间尺寸一致
        if depth_feat.shape[2:] != rgb_feat.shape[2:]:
            depth_feat = F.interpolate(depth_feat, size=(H, W), 
                                     mode='bilinear', align_corners=False)
        
        # 拼接特征
        concat_feat = torch.cat([rgb_feat, depth_feat], dim=1)
        
        # 空间注意力
        avg_pool = torch.mean(concat_feat, dim=1, keepdim=True)
        max_pool, _ = torch.max(concat_feat, dim=1, keepdim=True)
        spatial_input = torch.cat([avg_pool, max_pool], dim=1)
        spatial_att = self.spatial_attention(spatial_input)
        
        # 通道注意力
        channel_att = self.channel_attention(concat_feat)
        
        # 应用注意力
        attended_feat = concat_feat * spatial_att * channel_att
        
        # 特征融合
        fused_feat = self.fusion_conv(attended_feat)
        
        return self.act(self.bn(fused_feat))


class DepthRefinementModule(nn.Module):
    """
    深度细化模块
    提高深度图质量和一致性
    """
    
    def __init__(self, in_channels: int = 1, hidden_channels: int = 32):
        """
        初始化深度细化模块
        
        Args:
            in_channels: 输入深度通道数
            hidden_channels: 隐藏层通道数
        """
        super().__init__()
        
        self.refine_net = nn.Sequential(
            nn.Conv2d(in_channels, hidden_channels, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_channels, hidden_channels, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_channels, in_channels, 3, 1, 1),
            nn.Sigmoid()
        )
        
        # 边缘保持滤波器
        self.edge_filter = nn.Conv2d(in_channels, 1, 3, 1, 1, bias=False)
        
        # 初始化边缘检测核
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        edge_kernel = (sobel_x.abs() + sobel_y.abs()).unsqueeze(0).unsqueeze(0)
        self.edge_filter.weight.data = edge_kernel
        self.edge_filter.weight.requires_grad = False
    
    def forward(self, depth: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            depth: 输入深度图 [B, 1, H, W]
            
        Returns:
            细化后的深度图 [B, 1, H, W]
        """
        # 深度细化
        refined_depth = self.refine_net(depth)
        
        # 边缘保持
        edges = self.edge_filter(depth)
        edge_weight = torch.exp(-edges)  # 边缘处权重较小
        
        # 加权融合
        output = depth * edge_weight + refined_depth * (1 - edge_weight)
        
        return output


class MultiScaleFusion(nn.Module):
    """
    多尺度特征融合模块
    在不同尺度上融合RGB和深度特征
    """
    
    def __init__(self, channels: int, scales: list = [1, 2, 4]):
        """
        初始化多尺度融合模块
        
        Args:
            channels: 特征通道数
            scales: 多尺度因子列表
        """
        super().__init__()
        
        self.scales = scales
        self.fusion_convs = nn.ModuleList([
            nn.Conv2d(channels * 2, channels, 3, 1, 1) for _ in scales
        ])
        
        self.attention_weights = nn.Conv2d(len(scales) * channels, len(scales), 1)
        self.final_conv = nn.Conv2d(channels, channels, 1)
        
    def forward(self, rgb_feat: torch.Tensor, depth_feat: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            rgb_feat: RGB特征
            depth_feat: 深度特征
            
        Returns:
            多尺度融合后的特征
        """
        B, C, H, W = rgb_feat.shape
        scale_features = []
        
        for i, scale in enumerate(self.scales):
            if scale == 1:
                # 原始尺度
                rgb_s = rgb_feat
                depth_s = depth_feat
            else:
                # 下采样
                rgb_s = F.avg_pool2d(rgb_feat, scale, scale)
                depth_s = F.avg_pool2d(depth_feat, scale, scale)
            
            # 融合
            concat_s = torch.cat([rgb_s, depth_s], dim=1)
            fused_s = self.fusion_convs[i](concat_s)
            
            # 上采样回原始尺寸
            if scale != 1:
                fused_s = F.interpolate(fused_s, size=(H, W), 
                                      mode='bilinear', align_corners=False)
            
            scale_features.append(fused_s)
        
        # 自适应权重融合
        all_features = torch.cat(scale_features, dim=1)
        weights = F.softmax(self.attention_weights(all_features), dim=1)
        
        weighted_sum = sum(w.unsqueeze(1) * feat for w, feat in 
                          zip(weights.split(1, dim=1), scale_features))
        
        return self.final_conv(weighted_sum)


class DepthGuidedNMS(nn.Module):
    """
    深度引导的非极大值抑制
    利用深度信息改进目标检测的后处理
    """
    
    def __init__(self, depth_weight: float = 0.3):
        """
        初始化深度引导NMS
        
        Args:
            depth_weight: 深度信息权重
        """
        super().__init__()
        self.depth_weight = depth_weight
    
    def forward(self, boxes: torch.Tensor, scores: torch.Tensor, 
                depths: torch.Tensor, iou_threshold: float = 0.5) -> torch.Tensor:
        """
        深度引导的NMS
        
        Args:
            boxes: 检测框 [N, 4]
            scores: 置信度分数 [N]
            depths: 深度信息 [N]
            iou_threshold: IoU阈值
            
        Returns:
            保留的索引
        """
        # 标准NMS的基础上考虑深度信息
        # 深度相近的框更容易被抑制
        
        if boxes.numel() == 0:
            return torch.empty((0,), dtype=torch.long, device=boxes.device)
        
        # 计算IoU
        ious = self._box_iou(boxes, boxes)
        
        # 计算深度差异
        depth_diff = torch.abs(depths.unsqueeze(1) - depths.unsqueeze(0))
        depth_similarity = torch.exp(-depth_diff / depth_diff.mean())
        
        # 结合IoU和深度相似性
        combined_similarity = ious + self.depth_weight * depth_similarity
        
        # 执行NMS
        keep = []
        indices = torch.argsort(scores, descending=True)
        
        while indices.numel() > 0:
            current = indices[0]
            keep.append(current)
            
            if indices.numel() == 1:
                break
            
            # 计算与当前框的相似性
            similarities = combined_similarity[current, indices[1:]]
            
            # 保留相似性低于阈值的框
            indices = indices[1:][similarities <= iou_threshold]
        
        return torch.stack(keep) if keep else torch.empty((0,), dtype=torch.long, device=boxes.device)
    
    def _box_iou(self, boxes1: torch.Tensor, boxes2: torch.Tensor) -> torch.Tensor:
        """计算框的IoU"""
        area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
        area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
        
        lt = torch.max(boxes1[:, None, :2], boxes2[:, :2])
        rb = torch.min(boxes1[:, None, 2:], boxes2[:, 2:])
        
        wh = (rb - lt).clamp(min=0)
        inter = wh[:, :, 0] * wh[:, :, 1]
        
        union = area1[:, None] + area2 - inter
        
        return inter / union
