#!/usr/bin/env python3
"""
RGB+D数据集验证脚本
检查RGB图像和深度图的对应关系
"""

import os
import cv2
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def verify_rgbd_dataset(data_root="/home/<USER>/kitti"):
    """验证RGB+D数据集结构和对应关系"""
    
    print("=" * 60)
    print("🔍 RGB+D数据集验证")
    print("=" * 60)
    print(f"数据根目录: {data_root}")
    
    # 检查目录结构
    rgb_train_dir = os.path.join(data_root, "images/train_set")
    rgb_val_dir = os.path.join(data_root, "images/val_set")
    depth_train_dir = os.path.join(data_root, "depths/train_set")
    depth_val_dir = os.path.join(data_root, "depths/val_set")
    
    print("\n📁 目录结构检查:")
    dirs_to_check = [
        ("RGB训练集", rgb_train_dir),
        ("RGB验证集", rgb_val_dir),
        ("深度训练集", depth_train_dir),
        ("深度验证集", depth_val_dir)
    ]
    
    for name, dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            file_count = len([f for f in os.listdir(dir_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
            print(f"  ✅ {name}: {dir_path} ({file_count} 文件)")
        else:
            print(f"  ❌ {name}: {dir_path} (不存在)")
            return False
    
    # 检查文件对应关系
    print("\n🔗 RGB-深度对应关系检查:")
    
    # 检查训练集
    rgb_files = sorted([f for f in os.listdir(rgb_train_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    depth_files = sorted([f for f in os.listdir(depth_train_dir) if f.lower().endswith(('.png', '.tiff', '.npy'))])
    
    print(f"  RGB训练文件数: {len(rgb_files)}")
    print(f"  深度训练文件数: {len(depth_files)}")
    
    # 检查前10个文件的对应关系
    print("\n📋 文件对应关系检查 (前10个):")
    missing_count = 0
    
    for i, rgb_file in enumerate(rgb_files[:10]):
        rgb_name = Path(rgb_file).stem
        
        # 查找对应的深度文件
        depth_candidates = [
            f"{rgb_name}.png",
            f"{rgb_name}.tiff", 
            f"{rgb_name}.npy"
        ]
        
        depth_found = False
        for depth_candidate in depth_candidates:
            if depth_candidate in depth_files:
                print(f"  ✅ {rgb_file} ↔ {depth_candidate}")
                depth_found = True
                break
        
        if not depth_found:
            print(f"  ❌ {rgb_file} ↔ 未找到对应深度图")
            missing_count += 1
    
    if missing_count > 0:
        print(f"\n⚠️  警告: 前10个样本中有{missing_count}个缺少对应深度图")
    else:
        print(f"\n✅ 前10个样本RGB-深度对应关系正常")
    
    # 加载和分析样本
    print("\n🖼️  样本数据分析:")
    
    if len(rgb_files) > 0:
        # 加载第一个RGB图像
        rgb_path = os.path.join(rgb_train_dir, rgb_files[0])
        rgb_img = cv2.imread(rgb_path)
        
        if rgb_img is not None:
            print(f"  RGB图像尺寸: {rgb_img.shape}")
            print(f"  RGB数据类型: {rgb_img.dtype}")
            print(f"  RGB值范围: [{rgb_img.min()}, {rgb_img.max()}]")
        
        # 查找对应的深度图
        rgb_name = Path(rgb_files[0]).stem
        depth_path = None
        
        for ext in ['.png', '.tiff', '.npy']:
            candidate_path = os.path.join(depth_train_dir, f"{rgb_name}{ext}")
            if os.path.exists(candidate_path):
                depth_path = candidate_path
                break
        
        if depth_path:
            # 加载深度图
            if depth_path.endswith('.npy'):
                depth_img = np.load(depth_path)
            else:
                depth_img = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
            
            if depth_img is not None:
                print(f"  深度图尺寸: {depth_img.shape}")
                print(f"  深度数据类型: {depth_img.dtype}")
                print(f"  深度值范围: [{depth_img.min()}, {depth_img.max()}]")
                
                # 分析深度值分布
                valid_depth = depth_img[depth_img > 0]
                if len(valid_depth) > 0:
                    print(f"  有效深度值: {len(valid_depth)}/{depth_img.size} ({len(valid_depth)/depth_img.size*100:.1f}%)")
                    print(f"  有效深度范围: [{valid_depth.min():.2f}, {valid_depth.max():.2f}]")
                    print(f"  平均深度: {valid_depth.mean():.2f}")
                
                # 保存样本可视化
                save_sample_visualization(rgb_img, depth_img, rgb_files[0])
            else:
                print(f"  ❌ 无法加载深度图: {depth_path}")
        else:
            print(f"  ❌ 未找到对应深度图: {rgb_name}")
    
    # 统计总结
    print("\n📊 数据集统计:")
    print(f"  训练集RGB: {len(rgb_files)} 张")
    print(f"  训练集深度: {len(depth_files)} 张")
    
    # 验证集统计
    rgb_val_files = [f for f in os.listdir(rgb_val_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    depth_val_files = [f for f in os.listdir(depth_val_dir) if f.lower().endswith(('.png', '.tiff', '.npy'))]
    
    print(f"  验证集RGB: {len(rgb_val_files)} 张")
    print(f"  验证集深度: {len(depth_val_files)} 张")
    
    total_rgb = len(rgb_files) + len(rgb_val_files)
    total_depth = len(depth_files) + len(depth_val_files)
    
    print(f"  总计RGB: {total_rgb} 张")
    print(f"  总计深度: {total_depth} 张")
    
    # 给出建议
    print("\n💡 建议:")
    if total_rgb == total_depth:
        print("  ✅ RGB和深度图数量匹配，可以开始训练")
    else:
        print("  ⚠️  RGB和深度图数量不匹配，建议检查数据")
    
    if missing_count == 0:
        print("  ✅ 文件对应关系正常")
    else:
        print("  ⚠️  部分文件缺少对应关系，训练时会使用零深度图")
    
    return True

def save_sample_visualization(rgb_img, depth_img, filename):
    """保存RGB+深度图可视化样本"""
    try:
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # RGB图像
        rgb_display = cv2.cvtColor(rgb_img, cv2.COLOR_BGR2RGB)
        axes[0].imshow(rgb_display)
        axes[0].set_title('RGB Image')
        axes[0].axis('off')
        
        # 深度图
        depth_display = depth_img.copy()
        if len(depth_display.shape) == 3:
            depth_display = depth_display[:, :, 0]
        
        # 归一化深度图用于显示
        valid_mask = depth_display > 0
        if valid_mask.any():
            depth_norm = depth_display.copy().astype(np.float32)
            depth_norm[valid_mask] = (depth_norm[valid_mask] - depth_norm[valid_mask].min()) / \
                                   (depth_norm[valid_mask].max() - depth_norm[valid_mask].min())
            depth_norm[~valid_mask] = 0
        else:
            depth_norm = depth_display
        
        im = axes[1].imshow(depth_norm, cmap='jet')
        axes[1].set_title('Depth Map')
        axes[1].axis('off')
        plt.colorbar(im, ax=axes[1])
        
        # 深度直方图
        valid_depth = depth_img[depth_img > 0]
        if len(valid_depth) > 0:
            axes[2].hist(valid_depth, bins=50, alpha=0.7)
            axes[2].set_title('Depth Distribution')
            axes[2].set_xlabel('Depth Value')
            axes[2].set_ylabel('Frequency')
        
        plt.tight_layout()
        
        # 保存图像
        output_path = f"rgbd_sample_{Path(filename).stem}.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"  📸 样本可视化已保存: {output_path}")
        
    except Exception as e:
        print(f"  ⚠️  可视化保存失败: {e}")

if __name__ == "__main__":
    verify_rgbd_dataset()
