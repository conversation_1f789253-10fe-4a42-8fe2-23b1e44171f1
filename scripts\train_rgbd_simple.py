#!/usr/bin/env python3
"""
RGB+D简单融合模型训练脚本
4通道输入 (RGB+D) 版本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ultralytics import YOLO
import torch
import time
import json
from pathlib import Path

def main():
    print("=" * 60)
    print("🚀 RGB+D简单融合模型训练")
    print("=" * 60)
    print(f"使用设备: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")
    print(f"模型架构: YOLOv12m RGB+D Simple (4-channel)")
    print(f"融合策略: 早期融合 (RGB+D拼接)")
    print(f"数据集: KITTI (模拟RGB+D)")
    print("=" * 60)
    
    # 加载RGB+D简单融合模型
    model_config = 'ultralytics/cfg/models/v12/yolov12m-rgbd-simple.yaml'
    print(f"📋 加载模型配置: {model_config}")
    
    try:
        model = YOLO(model_config)
        print("✅ RGB+D简单融合模型加载成功")
        print(f"📊 模型参数量: {sum(p.numel() for p in model.model.parameters()):,}")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 训练参数配置
    train_args = {
        'data': 'datasets/kitti_simple.yaml',
        'epochs': 150,                    # RGB+D初期实验用较少轮数
        'batch': 28,                      # 稍小的batch size
        'imgsz': 640,
        'device': 0,
        'project': 'results/rgbd_simple',
        'name': 'rgbd_4channel_v1',
        'patience': 25,
        'save_period': 10,
        'workers': 10,
        
        # 优化器设置
        'lr0': 0.01,                      # 标准学习率
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        
        # 损失函数权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        
        # 数据增强 - 保守设置
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,                   # 禁用旋转
        'translate': 0.1,
        'scale': 0.5,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,                     # 禁用mixup
        'copy_paste': 0.0,
        
        # 验证和保存
        'val': True,
        'plots': True,
        'save': True,
        'verbose': True
    }
    
    print("🔧 训练参数配置:")
    for key, value in train_args.items():
        print(f"  {key}: {value}")
    print("=" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 开始训练
    try:
        print("🚀 开始RGB+D简单融合训练...")
        print("📊 预期改进:")
        print("  • 深度信息辅助目标检测")
        print("  • 4通道早期融合策略")
        print("  • 最小参数量增加")
        print("=" * 60)
        
        results = model.train(**train_args)
        
        # 训练完成
        end_time = time.time()
        training_time = end_time - start_time
        
        print("\n" + "=" * 60)
        print("✅ RGB+D简单融合训练完成！")
        print("=" * 60)
        print(f"训练时间: {training_time/3600:.2f} 小时")
        print(f"最佳权重: {model.trainer.best}")
        print(f"最后权重: {model.trainer.last}")
        print(f"结果目录: {model.trainer.save_dir}")
        
        # 验证最佳模型
        print("\n🔍 验证最佳RGB+D模型...")
        best_model = YOLO(model.trainer.best)
        val_results = best_model.val(data='datasets/kitti_simple.yaml')
        
        print("\n📊 RGB+D简单融合验证结果:")
        print(f"  mAP50: {val_results.box.map50:.4f}")
        print(f"  mAP50-95: {val_results.box.map:.4f}")
        
        # 按类别显示结果
        if hasattr(val_results.box, 'maps'):
            class_names = ['Car', 'Pedestrian', 'Cyclist']
            print("\n📈 各类别AP50 (RGB+D Simple):")
            for i, (name, ap) in enumerate(zip(class_names, val_results.box.maps)):
                print(f"  {name}: {ap:.4f}")
        
        # 保存实验结果
        experiment_results = {
            'model': 'yolov12m-rgbd-simple',
            'architecture': '4_channel_early_fusion',
            'epochs': 150,
            'training_time_hours': training_time/3600,
            'best_weights': str(model.trainer.best),
            'mAP50': float(val_results.box.map50),
            'mAP50_95': float(val_results.box.map),
            'fusion_strategy': 'early_fusion_4channel',
            'parameter_increase': 576,
            'parameter_increase_percent': 0.003,
            'final_results': {
                'Car_AP50': float(val_results.box.maps[0]) if hasattr(val_results.box, 'maps') else None,
                'Pedestrian_AP50': float(val_results.box.maps[1]) if hasattr(val_results.box, 'maps') else None,
                'Cyclist_AP50': float(val_results.box.maps[2]) if hasattr(val_results.box, 'maps') else None,
            }
        }
        
        # 保存结果到JSON
        results_file = f"{model.trainer.save_dir}/rgbd_simple_results.json"
        with open(results_file, 'w') as f:
            json.dump(experiment_results, f, indent=2)
        
        print(f"\n💾 RGB+D实验结果已保存到: {results_file}")
        print("=" * 60)
        print("🎯 RGB+D简单融合实验完成！")
        print("📊 可与RGB baseline进行性能对比分析")
        print("🔄 下一步可尝试更复杂的融合策略")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ RGB+D训练失败: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 可能的解决方案:")
        print("1. 检查数据集路径是否正确")
        print("2. 确认GPU内存是否足够")
        print("3. 验证模型配置文件")
        print("4. 检查数据加载器是否支持4通道")

if __name__ == "__main__":
    main()
