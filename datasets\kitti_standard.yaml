# KITTI Dataset Configuration for DALDet-style Evaluation
# 与DALDet论文一致的KITTI数据集配置
# 数据划分：训练集3712样本，验证集3769样本

# Dataset paths
path: D:\Program_Files\Pycharm_2024.1.7\Pycharm_Projects\dataset\kitti  # 数据集根目录
train: images/train_set   # 训练图像路径
val: images/val_set      # 验证图像路径

# Depth images (for RGB+D experiments)
depth_train: depths/train_set  # 训练深度图路径
depth_val: depths/val_set     # 验证深度图路径

# Labels
labels_train: labels/train_set  # 训练标签路径
labels_val: labels/val_set     # 验证标签路径

# Class information (KITTI 3 classes)
nc: 3  # number of classes
names:
  0: Car
  1: Pedestrian  
  2: Cyclist

# KITTI evaluation settings (matching DALDet paper)
evaluation:
  # AP calculation method
  ap_method: "R40"  # 40 recall points (KITTI standard)
  
  # IoU thresholds (class-specific)
  iou_thresholds:
    Car: 0.7        # Stricter for larger objects
    Pedestrian: 0.5 # More lenient for smaller objects
    Cyclist: 0.5    # More lenient for smaller objects
  
  # Difficulty levels
  difficulty_levels:
    Easy:
      min_height: 40    # pixels
      max_occlusion: 0  # fully visible
      max_truncation: 0.15
    Moderate:
      min_height: 25    # pixels  
      max_occlusion: 1  # partly occluded
      max_truncation: 0.30
    Hard:
      min_height: 25    # pixels
      max_occlusion: 2  # largely occluded  
      max_truncation: 0.50

# Post-processing settings (matching DALDet)
postprocess:
  conf_threshold: 0.001  # Low threshold for recall calculation
  iou_threshold: 0.3     # NMS IoU threshold
  max_det: 100          # Maximum detections per image
  
# Hardware settings for speed evaluation
hardware:
  device: "cuda:0"
  batch_size: 1        # For FPS measurement
  precision: "fp16"    # Half precision for speed

# Data augmentation (standard YOLO)
augment:
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  degrees: 0.0
  translate: 0.1
  scale: 0.5
  shear: 0.0
  perspective: 0.0
  flipud: 0.0
  fliplr: 0.5
  mosaic: 1.0
  mixup: 0.0
  copy_paste: 0.0

# Training settings
train_settings:
  epochs: 300
  batch_size: 16
  imgsz: 640
  optimizer: "AdamW"
  lr0: 0.001
  weight_decay: 0.0001
  warmup_epochs: 10
  warmup_momentum: 0.8
  warmup_bias_lr: 0.1
