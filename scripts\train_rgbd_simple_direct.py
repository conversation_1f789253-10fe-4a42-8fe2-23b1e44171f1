#!/usr/bin/env python3
"""
RGB+D训练脚本 - 直接方法
通过修改数据加载逻辑来支持4通道输入
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import cv2
import numpy as np
from pathlib import Path
import time
import json

# 首先修补数据加载逻辑
def patch_yolo_for_rgbd():
    """修补YOLO以支持RGB+D数据加载"""
    from ultralytics.data.dataset import YOLODataset
    
    # 保存原始的load_image方法
    if not hasattr(YOLODataset, '_original_load_image'):
        YOLODataset._original_load_image = YOLODataset.load_image
    
    def load_image_rgbd(self, i):
        """修改的load_image方法，支持RGB+D"""
        # 加载RGB图像
        im, (h0, w0), (h, w) = self._original_load_image(i)
        
        # 检查是否需要加载深度图
        if hasattr(self, 'use_rgbd') and self.use_rgbd:
            # 获取深度图路径
            img_path = self.im_files[i]
            depth_path = img_path.replace('/images/', '/depths/')
            
            if os.path.exists(depth_path):
                try:
                    # 加载深度图
                    depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
                    if depth is not None:
                        # 调整深度图尺寸
                        if depth.shape[:2] != (h, w):
                            depth = cv2.resize(depth, (w, h), interpolation=cv2.INTER_NEAREST)
                        
                        # 归一化深度值
                        if depth.max() > 255:
                            depth = (depth / depth.max() * 255).astype(np.uint8)
                        else:
                            depth = depth.astype(np.uint8)
                        
                        # 确保深度图是单通道
                        if len(depth.shape) == 3:
                            depth = depth[:, :, 0]
                        
                        # 添加通道维度
                        depth = np.expand_dims(depth, axis=2)
                        
                        # 合并RGB和深度
                        im = np.concatenate([im, depth], axis=2)
                        
                except Exception as e:
                    print(f"警告: 加载深度图失败 {depth_path}: {e}")
                    # 添加零深度通道
                    zero_depth = np.zeros((h, w, 1), dtype=im.dtype)
                    im = np.concatenate([im, zero_depth], axis=2)
            else:
                # 添加零深度通道
                zero_depth = np.zeros((h, w, 1), dtype=im.dtype)
                im = np.concatenate([im, zero_depth], axis=2)
        
        return im, (h0, w0), im.shape[:2]
    
    # 替换方法
    YOLODataset.load_image = load_image_rgbd
    print("✅ YOLO数据加载已修补为RGB+D模式")

def create_rgbd_trainer():
    """创建支持RGB+D的训练器"""
    from ultralytics import YOLO
    from ultralytics.data.build import build_dataset
    
    # 修补build_dataset函数
    original_build_dataset = build_dataset
    
    def build_dataset_rgbd(cfg, img_path, batch, data_info, mode="train", rect=False, stride=32):
        """修改的数据集构建函数"""
        dataset = original_build_dataset(cfg, img_path, batch, data_info, mode, rect, stride)
        
        # 检查是否是RGB+D配置
        if ('depth_train' in data_info or 'depth_val' in data_info or 
            ('input_channels' in data_info and data_info['input_channels'] == 4)):
            dataset.use_rgbd = True
            print(f"🔧 启用RGB+D模式 ({mode})")
        else:
            dataset.use_rgbd = False
        
        return dataset
    
    # 替换函数
    import ultralytics.data.build
    ultralytics.data.build.build_dataset = build_dataset_rgbd
    
    return YOLO

def main():
    print("=" * 60)
    print("🚀 YOLOv12 RGB+D训练 (直接方法)")
    print("=" * 60)
    print(f"使用设备: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")
    print(f"数据集: KITTI RGB+深度图")
    print("=" * 60)
    
    # 修补YOLO以支持RGB+D
    print("🔧 修补YOLO数据加载...")
    patch_yolo_for_rgbd()
    
    print("🔧 创建RGB+D训练器...")
    YOLO = create_rgbd_trainer()
    
    # 加载模型
    model_config = 'ultralytics/cfg/models/v12/yolov12m-rgbd-simple.yaml'
    print(f"📋 加载模型: {model_config}")
    
    try:
        model = YOLO(model_config)
        print("✅ RGB+D模型加载成功")
        
        total_params = sum(p.numel() for p in model.model.parameters())
        print(f"📊 模型参数量: {total_params:,}")
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 训练参数
    train_args = {
        'data': 'datasets/kitti_rgbd.yaml',
        'epochs': 100,                    # 先用较少轮数测试
        'batch': 16,                      # 较小batch size
        'imgsz': 640,
        'device': 0,
        'project': 'results/rgbd_direct',
        'name': 'kitti_rgbd_test',
        'patience': 20,
        'save_period': 10,
        'workers': 4,                     # 减少worker数量
        
        # 优化器
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        
        # 损失权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        
        # 保守的数据增强
        'hsv_h': 0.01,
        'hsv_s': 0.3,
        'hsv_v': 0.2,
        'degrees': 0.0,
        'translate': 0.05,
        'scale': 0.2,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 0.5,
        'mixup': 0.0,
        'copy_paste': 0.0,
        
        # 其他设置
        'val': True,
        'plots': True,
        'save': True,
        'verbose': True,
        'amp': True,
    }
    
    print("\n🔧 训练配置:")
    for key in ['epochs', 'batch', 'lr0', 'optimizer']:
        print(f"  {key}: {train_args[key]}")
    print("=" * 60)
    
    # 开始训练
    start_time = time.time()
    
    try:
        print("🚀 开始RGB+D训练...")
        print("📊 预期改进:")
        print("  • 深度信息辅助检测")
        print("  • 4通道早期融合")
        print("  • 小目标检测增强")
        print("=" * 60)
        
        results = model.train(**train_args)
        
        # 训练完成
        end_time = time.time()
        training_time = end_time - start_time
        
        print("\n" + "=" * 60)
        print("✅ RGB+D训练完成！")
        print("=" * 60)
        print(f"⏱️  训练时间: {training_time/3600:.2f} 小时")
        print(f"💾 最佳权重: {model.trainer.best}")
        print(f"📁 结果目录: {model.trainer.save_dir}")
        
        # 验证模型
        print("\n🔍 验证RGB+D模型...")
        best_model = YOLO(model.trainer.best)
        val_results = best_model.val(data='datasets/kitti_rgbd.yaml')
        
        print(f"\n📊 验证结果:")
        print(f"  mAP50: {val_results.box.map50:.4f}")
        print(f"  mAP50-95: {val_results.box.map:.4f}")
        
        # 保存结果
        results_data = {
            'model': 'yolov12m-rgbd-simple',
            'method': 'direct_patching',
            'training_time_hours': training_time/3600,
            'mAP50': float(val_results.box.map50),
            'mAP50_95': float(val_results.box.map),
            'best_weights': str(model.trainer.best)
        }
        
        results_file = f"{model.trainer.save_dir}/rgbd_results.json"
        with open(results_file, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        print(f"\n💾 结果已保存: {results_file}")
        print("🎯 RGB+D实验完成！")
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
