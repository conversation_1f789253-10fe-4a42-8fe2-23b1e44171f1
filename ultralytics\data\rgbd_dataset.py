"""
RGB+Depth Dataset for YOLOv12
支持RGB和深度图像的双模态数据加载
实现DALDet风格的数据处理
"""

import os
import cv2
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from ultralytics.data.dataset import YOLODataset
from ultralytics.utils import LOGGER


class RGBDDataset(YOLODataset):
    """
    RGB+Depth双模态数据集
    支持RGB图像和深度图像的同时加载和处理
    """
    
    def __init__(self, *args, depth_path: Optional[str] = None, 
                 depth_format: str = "png", **kwargs):
        """
        初始化RGB+Depth数据集
        
        Args:
            depth_path: 深度图像根目录路径
            depth_format: 深度图像格式 ("png", "tiff", "npy")
            *args, **kwargs: 传递给父类YOLODataset的参数
        """
        self.depth_path = depth_path
        self.depth_format = depth_format
        self.use_depth = depth_path is not None
        
        # 初始化父类
        super().__init__(*args, **kwargs)
        
        if self.use_depth:
            self._verify_depth_files()
            LOGGER.info(f"RGB+Depth数据集初始化完成，深度路径: {depth_path}")
        else:
            LOGGER.info("RGB数据集初始化完成（无深度信息）")
    
    def _verify_depth_files(self):
        """验证深度文件是否存在"""
        if not os.path.exists(self.depth_path):
            raise FileNotFoundError(f"深度图像路径不存在: {self.depth_path}")
        
        # 检查前几个文件是否存在对应的深度图
        missing_count = 0
        for i, img_path in enumerate(self.im_files[:min(10, len(self.im_files))]):
            depth_path = self._get_depth_path(img_path)
            if not os.path.exists(depth_path):
                missing_count += 1
        
        if missing_count > 0:
            LOGGER.warning(f"前10个样本中有{missing_count}个缺少对应的深度图")
    
    def _get_depth_path(self, img_path: str) -> str:
        """根据RGB图像路径获取对应的深度图路径"""
        img_path = Path(img_path)
        
        # 构建深度图路径
        # 假设深度图与RGB图像有相同的文件名但不同的扩展名
        depth_name = img_path.stem + f".{self.depth_format}"
        
        # 替换images为depth目录
        depth_path = Path(self.depth_path) / img_path.parent.name / depth_name
        
        return str(depth_path)
    
    def load_image(self, i: int) -> Tuple[np.ndarray, Optional[np.ndarray], Tuple[int, int], Tuple[int, int]]:
        """
        加载RGB图像和对应的深度图
        
        Args:
            i: 图像索引
            
        Returns:
            rgb_image: RGB图像 (H, W, 3)
            depth_image: 深度图像 (H, W, 1) 或 None
            original_shape: 原始图像尺寸 (H, W)
            resized_shape: 调整后尺寸 (H, W)
        """
        # 加载RGB图像（使用父类方法）
        rgb_image, original_shape, resized_shape = super().load_image(i)
        
        depth_image = None
        if self.use_depth:
            # 加载深度图像
            depth_path = self._get_depth_path(self.im_files[i])
            depth_image = self._load_depth_image(depth_path, resized_shape)
        
        return rgb_image, depth_image, original_shape, resized_shape
    
    def _load_depth_image(self, depth_path: str, target_shape: Tuple[int, int]) -> Optional[np.ndarray]:
        """
        加载和预处理深度图像
        
        Args:
            depth_path: 深度图像路径
            target_shape: 目标尺寸 (H, W)
            
        Returns:
            depth_image: 预处理后的深度图像 (H, W, 1)
        """
        if not os.path.exists(depth_path):
            LOGGER.warning(f"深度图像不存在: {depth_path}")
            # 返回零深度图
            return np.zeros((*target_shape, 1), dtype=np.float32)
        
        try:
            if self.depth_format == "npy":
                depth = np.load(depth_path)
            elif self.depth_format in ["png", "tiff"]:
                depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
                if depth is None:
                    raise ValueError(f"无法读取深度图像: {depth_path}")
            else:
                raise ValueError(f"不支持的深度图像格式: {self.depth_format}")
            
            # 确保深度图是单通道
            if len(depth.shape) == 3:
                depth = depth[:, :, 0]
            
            # 调整尺寸
            if depth.shape[:2] != target_shape:
                depth = cv2.resize(depth, (target_shape[1], target_shape[0]), 
                                 interpolation=cv2.INTER_NEAREST)
            
            # 归一化深度值
            depth = self._normalize_depth(depth)
            
            # 添加通道维度
            depth = np.expand_dims(depth, axis=2)
            
            return depth.astype(np.float32)
            
        except Exception as e:
            LOGGER.error(f"加载深度图像失败 {depth_path}: {e}")
            return np.zeros((*target_shape, 1), dtype=np.float32)
    
    def _normalize_depth(self, depth: np.ndarray) -> np.ndarray:
        """
        归一化深度值
        
        Args:
            depth: 原始深度图像
            
        Returns:
            normalized_depth: 归一化后的深度图像 [0, 1]
        """
        # 处理无效值
        depth = depth.astype(np.float32)
        valid_mask = (depth > 0) & (depth < np.inf) & (~np.isnan(depth))
        
        if not np.any(valid_mask):
            return np.zeros_like(depth)
        
        # 使用有效值的范围进行归一化
        min_depth = np.min(depth[valid_mask])
        max_depth = np.max(depth[valid_mask])
        
        if max_depth > min_depth:
            depth_normalized = np.zeros_like(depth)
            depth_normalized[valid_mask] = (depth[valid_mask] - min_depth) / (max_depth - min_depth)
        else:
            depth_normalized = np.zeros_like(depth)
        
        return depth_normalized
    
    def __getitem__(self, index: int) -> Dict:
        """
        获取单个样本
        
        Args:
            index: 样本索引
            
        Returns:
            sample: 包含RGB、深度图像和标签的字典
        """
        # 获取基础样本（RGB + 标签）
        sample = super().__getitem__(index)
        
        if self.use_depth:
            # 加载深度图像
            _, depth_image, _, _ = self.load_image(index)
            
            if depth_image is not None:
                # 转换为CHW格式
                depth_tensor = torch.from_numpy(depth_image.transpose(2, 0, 1))
                sample['depth'] = depth_tensor
            else:
                # 创建零深度图
                h, w = sample['img'].shape[1:]
                sample['depth'] = torch.zeros(1, h, w, dtype=torch.float32)
        
        return sample


class RGBDDataLoader:
    """RGB+Depth数据加载器工厂"""
    
    @staticmethod
    def create_dataset(data_config: Dict, mode: str = "train") -> RGBDDataset:
        """
        创建RGB+Depth数据集
        
        Args:
            data_config: 数据配置字典
            mode: 模式 ("train" 或 "val")
            
        Returns:
            dataset: RGB+Depth数据集实例
        """
        # 获取基础路径
        data_path = data_config.get('path', '')
        img_path = data_config.get(mode, '')
        
        # 获取深度图路径
        depth_key = f'depth_{mode}' if f'depth_{mode}' in data_config else 'depth_path'
        depth_path = data_config.get(depth_key, None)
        
        if depth_path:
            depth_path = os.path.join(data_path, depth_path)
        
        # 创建数据集
        dataset = RGBDDataset(
            img_path=os.path.join(data_path, img_path),
            imgsz=data_config.get('imgsz', 640),
            cache=data_config.get('cache', False),
            augment=(mode == 'train'),
            hyp=data_config.get('hyp', {}),
            prefix=f'{mode}: ',
            depth_path=depth_path,
            depth_format=data_config.get('depth_format', 'png')
        )
        
        return dataset


# 使用示例
if __name__ == "__main__":
    # 测试数据集
    config = {
        'path': '../datasets/kitti',
        'train': 'images/training',
        'val': 'images/testing',
        'depth_train': 'depth/training',
        'depth_val': 'depth/testing',
        'depth_format': 'png',
        'nc': 3,
        'names': {0: 'Car', 1: 'Pedestrian', 2: 'Cyclist'}
    }
    
    dataset = RGBDDataLoader.create_dataset(config, mode='train')
    print(f"数据集大小: {len(dataset)}")
    
    # 测试加载样本
    if len(dataset) > 0:
        sample = dataset[0]
        print(f"RGB图像形状: {sample['img'].shape}")
        if 'depth' in sample:
            print(f"深度图像形状: {sample['depth'].shape}")
        print(f"标签形状: {sample.get('bboxes', torch.tensor([])).shape}")
