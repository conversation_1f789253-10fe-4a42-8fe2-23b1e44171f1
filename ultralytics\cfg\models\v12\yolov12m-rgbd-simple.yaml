# Ultralytics YOLOv12m RGB+D Simple Fusion Model
# 简化版RGB+深度图融合，4通道输入
# 基于YOLOv12m架构，输入层改为4通道

# Parameters
nc: 3  # KITTI classes: <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Cyclist
ch: 4  # Input channels: RGB(3) + Depth(1)
scales: # model compound scaling constants
  # [depth, width, max_channels] - 保持与标准YOLOv12m相同
  m: [0.50, 1.00, 512]

# YOLOv12m backbone with 4-channel input (RGB+D)
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]           # 0-P1/2 (4->64 channels, RGB+D input)
  - [-1, 1, Conv, [128, 3, 2, 1, 2]]    # 1-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]   # 2
  - [-1, 1, Conv, [256, 3, 2, 1, 4]]    # 3-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]   # 4
  - [-1, 1, Conv, [512, 3, 2]]          # 5-P4/16
  - [-1, 4, A2C2f, [512, True, 4]]      # 6
  - [-1, 1, Conv, [1024, 3, 2]]         # 7-P5/32
  - [-1, 4, A2C2f, [1024, True, 1]]     # 8

# YOLOv12m head (unchanged)
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 6], 1, Concat, [1]] # cat backbone P4
  - [-1, 2, A2C2f, [512, False, -1]] # 11

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 4], 1, Concat, [1]] # cat backbone P3
  - [-1, 2, A2C2f, [256, False, -1]] # 14

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 11], 1, Concat, [1]] # cat head P4
  - [-1, 2, A2C2f, [512, False, -1]] # 17

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 8], 1, Concat, [1]] # cat head P5
  - [-1, 2, C3k2, [1024, True]] # 20 (P5/32-large)

  - [[14, 17, 20], 1, Detect, [nc]] # Detect(P3, P4, P5)
