"""
RGB+D数据加载器
专门处理RGB+深度图4通道输入的数据加载
"""

import cv2
import numpy as np
import torch
from pathlib import Path
import os
from ultralytics.data.dataset import YOLODataset
from ultralytics.utils import LOGGER

class RGBDDataset(YOLODataset):
    """
    RGB+D数据集类，继承自YOLODataset
    自动加载RGB图像和对应的深度图，合并为4通道输入
    """
    
    def __init__(self, *args, **kwargs):
        """初始化RGB+D数据集"""
        # 提取深度图相关参数
        self.depth_dir = kwargs.pop('depth_dir', None)
        self.depth_format = kwargs.pop('depth_format', 'png')
        self.depth_max = kwargs.pop('depth_max', 255.0)
        
        # 初始化父类
        super().__init__(*args, **kwargs)
        
        if self.depth_dir:
            LOGGER.info(f"RGB+D数据集初始化，深度目录: {self.depth_dir}")
            self._verify_depth_files()
        else:
            LOGGER.warning("未指定深度目录，将使用零深度图")
    
    def _verify_depth_files(self):
        """验证深度文件是否存在"""
        if not os.path.exists(self.depth_dir):
            LOGGER.warning(f"深度目录不存在: {self.depth_dir}")
            self.depth_dir = None
            return
        
        # 检查前几个文件
        missing_count = 0
        for i, img_path in enumerate(self.im_files[:min(10, len(self.im_files))]):
            depth_path = self._get_depth_path(img_path)
            if not os.path.exists(depth_path):
                missing_count += 1
        
        if missing_count > 0:
            LOGGER.warning(f"前10个样本中有{missing_count}个缺少深度图")
    
    def _get_depth_path(self, img_path):
        """根据RGB图像路径获取深度图路径"""
        img_path = Path(img_path)
        
        # 构建深度图路径
        # 假设深度图与RGB图像同名但在不同目录
        depth_name = img_path.stem + f".{self.depth_format}"
        
        # 将images路径替换为depths路径
        depth_path = str(img_path).replace('/images/', '/depths/')
        depth_path = Path(depth_path).parent / depth_name
        
        return str(depth_path)
    
    def load_image(self, i):
        """
        加载RGB图像和深度图，合并为4通道
        
        Returns:
            im: 4通道图像 (H, W, 4) - RGB + Depth
            (h0, w0): 原始尺寸
            im.shape[:2]: 当前尺寸
        """
        # 加载RGB图像（使用父类方法）
        im, (h0, w0), (h, w) = super().load_image(i)
        
        # 加载深度图
        depth = self._load_depth_image(i, (h, w))
        
        # 合并RGB和深度为4通道
        if depth is not None:
            # 确保深度图是单通道
            if len(depth.shape) == 3:
                depth = depth[:, :, 0:1]
            elif len(depth.shape) == 2:
                depth = np.expand_dims(depth, axis=2)
            
            # 拼接RGB和深度
            im_rgbd = np.concatenate([im, depth], axis=2)
        else:
            # 如果没有深度图，创建零深度通道
            zero_depth = np.zeros((h, w, 1), dtype=im.dtype)
            im_rgbd = np.concatenate([im, zero_depth], axis=2)
        
        return im_rgbd, (h0, w0), im_rgbd.shape[:2]
    
    def _load_depth_image(self, i, target_size):
        """加载深度图像"""
        if not self.depth_dir:
            return None
        
        # 获取深度图路径
        depth_path = self._get_depth_path(self.im_files[i])
        
        if not os.path.exists(depth_path):
            return None
        
        try:
            # 加载深度图
            if self.depth_format == 'npy':
                depth = np.load(depth_path)
            else:
                depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
            
            if depth is None:
                return None
            
            # 调整尺寸
            h, w = target_size
            if depth.shape[:2] != (h, w):
                depth = cv2.resize(depth, (w, h), interpolation=cv2.INTER_NEAREST)
            
            # 归一化深度值到0-255范围
            if depth.max() > 255:
                depth = (depth / depth.max() * 255).astype(np.uint8)
            else:
                depth = depth.astype(np.uint8)
            
            return depth
            
        except Exception as e:
            LOGGER.warning(f"加载深度图失败 {depth_path}: {e}")
            return None


def build_rgbd_dataset(cfg, img_path, batch, data_info, mode="train", rect=False, stride=32):
    """
    构建RGB+D数据集
    
    Args:
        cfg: 配置字典
        img_path: 图像路径
        batch: 批次大小
        data_info: 数据信息
        mode: 模式 ("train" 或 "val")
        rect: 是否使用矩形训练
        stride: 步长
    
    Returns:
        dataset: RGB+D数据集实例
    """
    # 获取深度图路径
    depth_key = f'depth_{mode}' if f'depth_{mode}' in data_info else None
    depth_dir = None
    
    if depth_key and depth_key in data_info:
        data_root = data_info.get('path', '')
        depth_rel_path = data_info[depth_key]
        depth_dir = os.path.join(data_root, depth_rel_path)
    
    # 创建数据集
    dataset = RGBDDataset(
        img_path=img_path,
        imgsz=cfg.imgsz,
        batch_size=batch,
        augment=mode == "train",
        hyp=cfg,
        rect=rect,
        cache=cfg.cache or None,
        single_cls=cfg.single_cls or False,
        stride=int(stride),
        pad=0.0 if mode == "train" else 0.5,
        prefix=f"{mode}: ",
        task=cfg.task,
        classes=cfg.classes,
        data=data_info,
        fraction=cfg.fraction if mode == "train" else 1.0,
        # RGB+D特定参数
        depth_dir=depth_dir,
        depth_format=data_info.get('depth_config', {}).get('format', 'png'),
        depth_max=data_info.get('depth_config', {}).get('max_distance', 80.0)
    )
    
    return dataset


# 简化的RGB+D数据集注册
def register_rgbd_dataset():
    """注册RGB+D数据集构建函数"""
    try:
        # 动态替换build_dataset函数
        import ultralytics.data.build

        # 保存原始函数
        if not hasattr(ultralytics.data.build, '_original_build_dataset'):
            ultralytics.data.build._original_build_dataset = ultralytics.data.build.build_dataset

        def patched_build_dataset(cfg, img_path, batch, data_info, mode="train", rect=False, stride=32):
            """修补的数据集构建函数"""
            # 检查是否是RGB+D配置
            if ('depth_train' in data_info or 'depth_val' in data_info or
                ('input_channels' in data_info and data_info['input_channels'] == 4)):
                LOGGER.info(f"🔧 使用RGB+D数据集加载器 (模式: {mode})")
                return build_rgbd_dataset(cfg, img_path, batch, data_info, mode, rect, stride)
            else:
                # 使用原始函数
                return ultralytics.data.build._original_build_dataset(cfg, img_path, batch, data_info, mode, rect, stride)

        # 替换函数
        ultralytics.data.build.build_dataset = patched_build_dataset

        LOGGER.info("✅ RGB+D数据集构建函数已注册")
        return True

    except Exception as e:
        LOGGER.error(f"❌ RGB+D数据集注册失败: {e}")
        return False


if __name__ == "__main__":
    # 测试RGB+D数据集
    test_config = {
        'path': '/home/<USER>/kitti',
        'train': 'images/train_set',
        'depth_train': 'depths/train_set',
        'input_channels': 4,
        'depth_config': {
            'format': 'png',
            'max_distance': 80.0
        }
    }
    
    dataset = build_rgbd_dataset(
        cfg=type('cfg', (), {'imgsz': 640, 'cache': False, 'single_cls': False, 'task': 'detect', 'classes': None, 'fraction': 1.0})(),
        img_path=os.path.join(test_config['path'], test_config['train']),
        batch=1,
        data_info=test_config,
        mode='train'
    )
    
    print(f"数据集大小: {len(dataset)}")
    if len(dataset) > 0:
        sample = dataset[0]
        print(f"样本图像形状: {sample['img'].shape}")  # 应该是 [4, H, W]
