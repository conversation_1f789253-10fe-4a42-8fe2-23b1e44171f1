#!/usr/bin/env python3
"""
标准RGB Baseline训练脚本
使用预训练YOLOv12m.pt，300个epoch
作为所有RGB+D实验的基准对比数据
"""

import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ultralytics import YOLO
import torch
import time

def main():
    print("=" * 60)
    print("🚀 标准RGB Baseline训练 - YOLOv12m")
    print("=" * 60)
    print(f"使用设备: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")
    print(f"预训练模型: yolov12m.pt")
    print(f"训练轮数: 300 epochs")
    print(f"数据集: KITTI (RGB-only)")
    print("=" * 60)
    
    # 加载预训练模型
    model = YOLO('yolov12m.pt')
    
    # 训练参数 - 标准配置
    train_args = {
        'data': 'datasets/kitti_simple.yaml',
        'epochs': 300,                    # 300个epoch充分训练
        'batch': 32,                      # RTX 4090适合的batch size
        'imgsz': 640,
        'device': 0,
        'project': 'results/baseline',
        'name': 'rgb_standard_300ep',
        'patience': 50,                   # 更大的早停耐心值
        'save_period': 20,                # 每20个epoch保存一次
        'workers': 16,
        
        # 优化器设置
        'lr0': 0.01,
        'lrf': 0.01,
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        
        # 损失函数权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
        
        # 数据增强
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.1,
        
        # 验证和保存
        'val': True,
        'plots': True,
        'save': True,
        'verbose': True
    }
    
    print("训练参数:")
    for key, value in train_args.items():
        print(f"  {key}: {value}")
    print("=" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 开始训练
    try:
        print("🚀 开始标准RGB Baseline训练...")
        results = model.train(**train_args)
        
        # 训练完成
        end_time = time.time()
        training_time = end_time - start_time
        
        print("\n" + "=" * 60)
        print("✅ 标准RGB Baseline训练完成！")
        print("=" * 60)
        print(f"训练时间: {training_time/3600:.2f} 小时")
        print(f"最佳权重: {model.trainer.best}")
        print(f"最后权重: {model.trainer.last}")
        print(f"结果目录: {model.trainer.save_dir}")
        
        # 验证最佳模型
        print("\n🔍 验证最佳模型...")
        best_model = YOLO(model.trainer.best)
        val_results = best_model.val(data='datasets/kitti_simple.yaml')
        
        print("\n📊 最终验证结果:")
        print(f"  mAP50: {val_results.box.map50:.4f}")
        print(f"  mAP50-95: {val_results.box.map:.4f}")
        
        # 按类别显示结果
        if hasattr(val_results.box, 'maps'):
            class_names = ['Car', 'Pedestrian', 'Cyclist']
            print("\n📈 各类别AP50:")
            for i, (name, ap) in enumerate(zip(class_names, val_results.box.maps)):
                print(f"  {name}: {ap:.4f}")
        
        # 保存基准结果
        baseline_results = {
            'model': 'yolov12m.pt',
            'epochs': 300,
            'training_time_hours': training_time/3600,
            'best_weights': str(model.trainer.best),
            'mAP50': float(val_results.box.map50),
            'mAP50_95': float(val_results.box.map),
            'final_results': {
                'Car_AP50': float(val_results.box.maps[0]) if hasattr(val_results.box, 'maps') else None,
                'Pedestrian_AP50': float(val_results.box.maps[1]) if hasattr(val_results.box, 'maps') else None,
                'Cyclist_AP50': float(val_results.box.maps[2]) if hasattr(val_results.box, 'maps') else None,
            }
        }
        
        # 保存结果到JSON
        import json
        results_file = f"{model.trainer.save_dir}/baseline_results.json"
        with open(results_file, 'w') as f:
            json.dump(baseline_results, f, indent=2)
        
        print(f"\n💾 基准结果已保存到: {results_file}")
        print("=" * 60)
        print("🎯 此结果将作为所有RGB+D实验的对比基准！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
