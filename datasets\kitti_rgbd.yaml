# KITTI RGB+D Dataset Configuration
# 真实RGB+深度图双模态数据集配置

# 数据集路径
path: /home/<USER>/kitti
train: images/train_set
val: images/val_set

# 深度图路径 (与RGB图像一一对应)
depth_train: depths/train_set
depth_val: depths/val_set

# 深度图配置
depth_config:
  format: "png"                    # 深度图格式: png, tiff, npy
  max_distance: 80.0               # KITTI最大深度距离(米)
  normalization: "minmax"          # 归一化方式: minmax, zscore, none
  fill_missing: true               # 是否填充缺失深度值
  missing_value: 0.0               # 缺失深度的填充值

# 输入配置
input_channels: 4                  # RGB(3) + Depth(1)
fusion_mode: "early"               # 融合模式: early, late, attention

# 类别配置
nc: 3
names:
  0: Car
  1: Pedestrian  
  2: Cyclist

# RGB+D数据增强配置
augmentation:
  # RGB增强
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  
  # 几何变换 (RGB和深度同步)
  degrees: 0.0                     # 禁用旋转(深度图敏感)
  translate: 0.1
  scale: 0.5
  shear: 0.0
  perspective: 0.0
  flipud: 0.0
  fliplr: 0.5                      # 水平翻转
  
  # 深度特定增强
  depth_noise_std: 0.02            # 深度噪声标准差
  depth_dropout: 0.05              # 深度值随机丢失概率
  depth_blur_prob: 0.1             # 深度图模糊概率
  
  # 混合增强
  mosaic: 0.8                      # 减少mosaic强度
  mixup: 0.0                       # 禁用mixup(双模态复杂)
  copy_paste: 0.0                  # 禁用copy_paste

# 训练配置
train_config:
  batch_size: 24                   # 适应双模态内存需求
  workers: 8                       # 数据加载线程数
  pin_memory: true                 # 固定内存
  persistent_workers: true         # 持久化worker

# 验证配置
val_config:
  batch_size: 32
  workers: 4

# 硬件配置
hardware:
  device: "cuda:0"
  mixed_precision: true            # 混合精度训练
  compile: false                   # PyTorch编译优化
