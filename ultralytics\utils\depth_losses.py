"""
深度引导的损失函数
实现DALDet风格的深度感知损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Dict, Optional
import numpy as np


class DepthConsistencyLoss(nn.Module):
    """
    深度一致性损失
    确保检测框与深度信息的一致性
    """
    
    def __init__(self, weight: float = 1.0, smooth_l1_beta: float = 1.0):
        """
        初始化深度一致性损失
        
        Args:
            weight: 损失权重
            smooth_l1_beta: Smooth L1损失的beta参数
        """
        super().__init__()
        self.weight = weight
        self.smooth_l1_beta = smooth_l1_beta
    
    def forward(self, pred_boxes: torch.Tensor, target_boxes: torch.Tensor,
                depth_map: torch.Tensor, valid_mask: torch.Tensor) -> torch.Tensor:
        """
        计算深度一致性损失
        
        Args:
            pred_boxes: 预测框 [N, 4] (x1, y1, x2, y2)
            target_boxes: 真值框 [N, 4]
            depth_map: 深度图 [B, 1, H, W]
            valid_mask: 有效掩码 [N]
            
        Returns:
            深度一致性损失
        """
        if not valid_mask.any():
            return torch.tensor(0.0, device=pred_boxes.device, requires_grad=True)
        
        # 提取有效的框
        valid_pred = pred_boxes[valid_mask]
        valid_target = target_boxes[valid_mask]
        
        # 计算框内深度统计
        pred_depths = self._extract_box_depths(valid_pred, depth_map)
        target_depths = self._extract_box_depths(valid_target, depth_map)
        
        # 计算深度一致性损失
        depth_diff = pred_depths - target_depths
        consistency_loss = F.smooth_l1_loss(depth_diff, torch.zeros_like(depth_diff), 
                                          beta=self.smooth_l1_beta)
        
        return self.weight * consistency_loss
    
    def _extract_box_depths(self, boxes: torch.Tensor, depth_map: torch.Tensor) -> torch.Tensor:
        """
        提取框内的深度统计信息
        
        Args:
            boxes: 检测框 [N, 4]
            depth_map: 深度图 [B, 1, H, W]
            
        Returns:
            框内平均深度 [N]
        """
        B, _, H, W = depth_map.shape
        N = boxes.shape[0]
        
        # 归一化坐标到[0, 1]
        normalized_boxes = boxes.clone()
        normalized_boxes[:, [0, 2]] /= W
        normalized_boxes[:, [1, 3]] /= H
        normalized_boxes = normalized_boxes.clamp(0, 1)
        
        depths = []
        for i in range(N):
            x1, y1, x2, y2 = normalized_boxes[i]
            
            # 转换为像素坐标
            px1, py1 = int(x1 * W), int(y1 * H)
            px2, py2 = int(x2 * W), int(y2 * H)
            
            # 提取框内深度
            if px2 > px1 and py2 > py1:
                box_depth = depth_map[0, 0, py1:py2, px1:px2]
                valid_depth = box_depth[box_depth > 0]
                
                if valid_depth.numel() > 0:
                    avg_depth = valid_depth.mean()
                else:
                    avg_depth = torch.tensor(0.0, device=depth_map.device)
            else:
                avg_depth = torch.tensor(0.0, device=depth_map.device)
            
            depths.append(avg_depth)
        
        return torch.stack(depths)


class DistanceAwareLocalizationLoss(nn.Module):
    """
    距离感知定位损失
    根据目标距离调整定位精度要求
    """
    
    def __init__(self, weight: float = 1.0, distance_threshold: float = 30.0):
        """
        初始化距离感知定位损失
        
        Args:
            weight: 损失权重
            distance_threshold: 距离阈值（米）
        """
        super().__init__()
        self.weight = weight
        self.distance_threshold = distance_threshold
    
    def forward(self, pred_boxes: torch.Tensor, target_boxes: torch.Tensor,
                target_depths: torch.Tensor, valid_mask: torch.Tensor) -> torch.Tensor:
        """
        计算距离感知定位损失
        
        Args:
            pred_boxes: 预测框 [N, 4]
            target_boxes: 真值框 [N, 4]
            target_depths: 目标深度 [N]
            valid_mask: 有效掩码 [N]
            
        Returns:
            距离感知定位损失
        """
        if not valid_mask.any():
            return torch.tensor(0.0, device=pred_boxes.device, requires_grad=True)
        
        # 提取有效数据
        valid_pred = pred_boxes[valid_mask]
        valid_target = target_boxes[valid_mask]
        valid_depths = target_depths[valid_mask]
        
        # 计算基础定位损失（IoU Loss）
        iou_loss = self._compute_iou_loss(valid_pred, valid_target)
        
        # 计算距离权重
        distance_weights = self._compute_distance_weights(valid_depths)
        
        # 应用距离权重
        weighted_loss = iou_loss * distance_weights
        
        return self.weight * weighted_loss.mean()
    
    def _compute_iou_loss(self, pred_boxes: torch.Tensor, target_boxes: torch.Tensor) -> torch.Tensor:
        """计算IoU损失"""
        # 计算IoU
        ious = self._box_iou(pred_boxes, target_boxes)
        
        # IoU损失
        iou_loss = 1 - ious
        
        return iou_loss
    
    def _compute_distance_weights(self, depths: torch.Tensor) -> torch.Tensor:
        """
        计算距离权重
        近距离目标要求更高的定位精度
        """
        # 距离越近，权重越大
        weights = torch.exp(-depths / self.distance_threshold)
        
        # 归一化权重
        weights = weights / weights.mean()
        
        return weights
    
    def _box_iou(self, boxes1: torch.Tensor, boxes2: torch.Tensor) -> torch.Tensor:
        """计算框的IoU"""
        # 计算交集
        lt = torch.max(boxes1[:, :2], boxes2[:, :2])
        rb = torch.min(boxes1[:, 2:], boxes2[:, 2:])
        
        wh = (rb - lt).clamp(min=0)
        inter = wh[:, 0] * wh[:, 1]
        
        # 计算并集
        area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
        area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
        union = area1 + area2 - inter
        
        # 计算IoU
        iou = inter / (union + 1e-6)
        
        return iou


class MultiScaleDepthLoss(nn.Module):
    """
    多尺度深度损失
    在不同尺度上保持深度特征的一致性
    """
    
    def __init__(self, weight: float = 1.0, scales: list = [1, 2, 4]):
        """
        初始化多尺度深度损失
        
        Args:
            weight: 损失权重
            scales: 多尺度因子
        """
        super().__init__()
        self.weight = weight
        self.scales = scales
    
    def forward(self, pred_depth_features: Dict[str, torch.Tensor],
                target_depth_features: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        计算多尺度深度损失
        
        Args:
            pred_depth_features: 预测的深度特征字典
            target_depth_features: 目标深度特征字典
            
        Returns:
            多尺度深度损失
        """
        total_loss = 0.0
        num_scales = 0
        
        for scale in self.scales:
            scale_key = f'scale_{scale}'
            
            if scale_key in pred_depth_features and scale_key in target_depth_features:
                pred_feat = pred_depth_features[scale_key]
                target_feat = target_depth_features[scale_key]
                
                # 确保尺寸一致
                if pred_feat.shape != target_feat.shape:
                    target_feat = F.interpolate(target_feat, size=pred_feat.shape[2:],
                                              mode='bilinear', align_corners=False)
                
                # 计算特征损失
                scale_loss = F.mse_loss(pred_feat, target_feat)
                total_loss += scale_loss
                num_scales += 1
        
        if num_scales > 0:
            return self.weight * total_loss / num_scales
        else:
            return torch.tensor(0.0, device=list(pred_depth_features.values())[0].device,
                              requires_grad=True)


class DepthGuidedFocalLoss(nn.Module):
    """
    深度引导的Focal损失
    根据深度信息调整难易样本的权重
    """
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, 
                 depth_weight: float = 0.5):
        """
        初始化深度引导Focal损失
        
        Args:
            alpha: 平衡因子
            gamma: 聚焦参数
            depth_weight: 深度权重
        """
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.depth_weight = depth_weight
    
    def forward(self, pred_logits: torch.Tensor, targets: torch.Tensor,
                depths: torch.Tensor) -> torch.Tensor:
        """
        计算深度引导的Focal损失
        
        Args:
            pred_logits: 预测logits [N, C]
            targets: 目标标签 [N]
            depths: 深度信息 [N]
            
        Returns:
            深度引导的Focal损失
        """
        # 计算标准Focal损失
        ce_loss = F.cross_entropy(pred_logits, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        
        # Alpha权重
        alpha_t = self.alpha * targets + (1 - self.alpha) * (1 - targets)
        
        # Focal权重
        focal_weight = alpha_t * (1 - pt) ** self.gamma
        
        # 深度权重（远距离目标更难检测）
        depth_weight = torch.exp(depths / 50.0)  # 50米为参考距离
        depth_weight = depth_weight / depth_weight.mean()
        
        # 组合权重
        combined_weight = focal_weight * (1 + self.depth_weight * depth_weight)
        
        # 最终损失
        focal_loss = combined_weight * ce_loss
        
        return focal_loss.mean()


class CombinedDepthLoss(nn.Module):
    """
    组合深度损失
    整合多种深度相关的损失函数
    """
    
    def __init__(self, 
                 consistency_weight: float = 1.0,
                 localization_weight: float = 1.0,
                 multiscale_weight: float = 0.5,
                 focal_weight: float = 0.5):
        """
        初始化组合深度损失
        
        Args:
            consistency_weight: 深度一致性损失权重
            localization_weight: 距离感知定位损失权重
            multiscale_weight: 多尺度深度损失权重
            focal_weight: 深度引导Focal损失权重
        """
        super().__init__()
        
        self.depth_consistency = DepthConsistencyLoss(consistency_weight)
        self.distance_localization = DistanceAwareLocalizationLoss(localization_weight)
        self.multiscale_depth = MultiScaleDepthLoss(multiscale_weight)
        self.depth_focal = DepthGuidedFocalLoss()
        
        self.focal_weight = focal_weight
    
    def forward(self, predictions: Dict, targets: Dict, depth_info: Dict) -> Dict[str, torch.Tensor]:
        """
        计算组合深度损失
        
        Args:
            predictions: 预测结果字典
            targets: 目标字典
            depth_info: 深度信息字典
            
        Returns:
            各项损失的字典
        """
        losses = {}
        
        # 深度一致性损失
        if 'boxes' in predictions and 'boxes' in targets:
            consistency_loss = self.depth_consistency(
                predictions['boxes'], targets['boxes'],
                depth_info['depth_map'], targets['valid_mask']
            )
            losses['depth_consistency'] = consistency_loss
        
        # 距离感知定位损失
        if 'boxes' in predictions and 'boxes' in targets and 'depths' in targets:
            localization_loss = self.distance_localization(
                predictions['boxes'], targets['boxes'],
                targets['depths'], targets['valid_mask']
            )
            losses['distance_localization'] = localization_loss
        
        # 多尺度深度损失
        if 'depth_features' in predictions and 'depth_features' in targets:
            multiscale_loss = self.multiscale_depth(
                predictions['depth_features'], targets['depth_features']
            )
            losses['multiscale_depth'] = multiscale_loss
        
        # 深度引导Focal损失
        if 'logits' in predictions and 'labels' in targets and 'depths' in targets:
            focal_loss = self.depth_focal(
                predictions['logits'], targets['labels'], targets['depths']
            )
            losses['depth_focal'] = self.focal_weight * focal_loss
        
        # 总损失
        total_loss = sum(losses.values())
        losses['total_depth_loss'] = total_loss
        
        return losses
