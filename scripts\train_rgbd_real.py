#!/usr/bin/env python3
"""
真实RGB+D数据集训练脚本
使用KITTI RGB+深度图进行YOLOv12训练
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ultralytics import YOLO
import torch
import time
import json
from pathlib import Path

# 导入并注册RGB+D数据加载器
from ultralytics.data.rgbd_loader import register_rgbd_dataset

def main():
    print("=" * 60)
    print("🚀 YOLOv12 RGB+D真实数据集训练")
    print("=" * 60)
    print(f"使用设备: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")
    print(f"数据集: KITTI RGB+深度图 (7,481对)")
    print(f"训练集: 3,712对 | 验证集: 3,769对")
    print(f"模型: YOLOv12m RGB+D Simple (4通道)")
    print("=" * 60)

    # 注册RGB+D数据加载器
    print("🔧 注册RGB+D数据加载器...")
    register_rgbd_dataset()
    print("✅ RGB+D数据加载器注册成功")
    
    # 加载RGB+D模型
    model_config = 'ultralytics/cfg/models/v12/yolov12m-rgbd-simple.yaml'
    print(f"📋 加载模型配置: {model_config}")
    
    try:
        model = YOLO(model_config)
        print("✅ RGB+D模型加载成功")
        
        # 显示模型信息
        total_params = sum(p.numel() for p in model.model.parameters())
        trainable_params = sum(p.numel() for p in model.model.parameters() if p.requires_grad)
        print(f"📊 总参数量: {total_params:,}")
        print(f"📊 可训练参数: {trainable_params:,}")
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 训练参数配置
    train_args = {
        # 数据配置
        'data': 'datasets/kitti_rgbd.yaml',
        'epochs': 200,                    # RGB+D需要更多训练轮数
        'batch': 24,                      # 适应4通道内存需求
        'imgsz': 640,
        'device': 0,
        
        # 项目配置
        'project': 'results/rgbd_real',
        'name': 'kitti_rgbd_4ch_v1',
        'exist_ok': True,
        
        # 训练控制
        'patience': 30,                   # 更大的早停耐心
        'save_period': 10,                # 每10轮保存
        'workers': 8,                     # 数据加载线程
        'seed': 42,                       # 随机种子
        
        # 优化器设置
        'optimizer': 'AdamW',             # AdamW优化器
        'lr0': 0.001,                     # 较小的初始学习率
        'lrf': 0.01,                      # 最终学习率比例
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 5.0,             # 更长的warmup
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        
        # 损失函数权重
        'box': 7.5,                       # 边界框损失权重
        'cls': 0.5,                       # 分类损失权重  
        'dfl': 1.5,                       # DFL损失权重
        
        # 数据增强 - 针对RGB+D优化
        'hsv_h': 0.01,                    # 减少色调变化
        'hsv_s': 0.5,                     # 减少饱和度变化
        'hsv_v': 0.3,                     # 减少亮度变化
        'degrees': 0.0,                   # 禁用旋转(深度敏感)
        'translate': 0.1,                 # 轻微平移
        'scale': 0.3,                     # 减少缩放变化
        'shear': 0.0,                     # 禁用剪切
        'perspective': 0.0,               # 禁用透视变换
        'flipud': 0.0,                    # 禁用垂直翻转
        'fliplr': 0.5,                    # 保持水平翻转
        'mosaic': 0.8,                    # 减少mosaic强度
        'mixup': 0.0,                     # 禁用mixup
        'copy_paste': 0.0,                # 禁用copy_paste
        
        # 验证和保存
        'val': True,
        'plots': True,
        'save': True,
        'save_txt': False,
        'save_conf': False,
        'verbose': True,
        
        # 性能优化
        'amp': True,                      # 自动混合精度
        'fraction': 1.0,                  # 使用全部数据
        'profile': False,                 # 禁用性能分析
        'freeze': None,                   # 不冻结层
    }
    
    print("\n🔧 训练参数配置:")
    key_params = ['epochs', 'batch', 'lr0', 'optimizer', 'patience', 'amp']
    for key in key_params:
        if key in train_args:
            print(f"  {key}: {train_args[key]}")
    print("=" * 60)
    
    # 记录开始时间
    start_time = time.time()
    
    # 开始训练
    try:
        print("🚀 开始RGB+D真实数据训练...")
        print("📊 预期改进:")
        print("  • 深度信息增强小目标检测")
        print("  • 4通道早期融合策略")
        print("  • KITTI真实场景适应")
        print("  • 自动驾驶目标检测优化")
        print("=" * 60)
        
        # 执行训练
        results = model.train(**train_args)
        
        # 训练完成统计
        end_time = time.time()
        training_time = end_time - start_time
        
        print("\n" + "=" * 60)
        print("✅ RGB+D真实数据训练完成！")
        print("=" * 60)
        print(f"⏱️  训练时间: {training_time/3600:.2f} 小时")
        print(f"💾 最佳权重: {model.trainer.best}")
        print(f"💾 最后权重: {model.trainer.last}")
        print(f"📁 结果目录: {model.trainer.save_dir}")
        
        # 验证最佳模型
        print("\n🔍 验证最佳RGB+D模型...")
        best_model = YOLO(model.trainer.best)
        val_results = best_model.val(data='datasets/kitti_rgbd.yaml')
        
        print("\n📊 RGB+D模型验证结果:")
        print(f"  mAP50: {val_results.box.map50:.4f}")
        print(f"  mAP50-95: {val_results.box.map:.4f}")
        
        # 按类别显示结果
        if hasattr(val_results.box, 'maps') and len(val_results.box.maps) >= 3:
            class_names = ['Car', 'Pedestrian', 'Cyclist']
            print("\n📈 各类别AP50 (RGB+D vs 目标):")
            targets = [0.92, 0.70, 0.65]  # 改进目标
            
            for i, (name, ap, target) in enumerate(zip(class_names, val_results.box.maps, targets)):
                improvement = "✅" if ap >= target else "🔄"
                print(f"  {name}: {ap:.4f} (目标: {target:.2f}) {improvement}")
        
        # 保存详细实验结果
        experiment_results = {
            'experiment_info': {
                'model': 'yolov12m-rgbd-simple',
                'dataset': 'kitti_real_rgbd',
                'total_samples': 7481,
                'train_samples': 3712,
                'val_samples': 3769,
                'input_channels': 4,
                'fusion_strategy': 'early_4channel'
            },
            'training_config': {
                'epochs': train_args['epochs'],
                'batch_size': train_args['batch'],
                'optimizer': train_args['optimizer'],
                'learning_rate': train_args['lr0'],
                'weight_decay': train_args['weight_decay'],
                'amp': train_args['amp']
            },
            'results': {
                'training_time_hours': training_time/3600,
                'best_weights': str(model.trainer.best),
                'mAP50': float(val_results.box.map50),
                'mAP50_95': float(val_results.box.map),
                'parameter_count': total_params,
                'trainable_parameters': trainable_params
            },
            'class_results': {}
        }
        
        # 添加类别结果
        if hasattr(val_results.box, 'maps') and len(val_results.box.maps) >= 3:
            class_names = ['Car', 'Pedestrian', 'Cyclist']
            for i, (name, ap) in enumerate(zip(class_names, val_results.box.maps)):
                experiment_results['class_results'][f'{name}_AP50'] = float(ap)
        
        # 保存结果
        results_file = f"{model.trainer.save_dir}/rgbd_real_experiment.json"
        with open(results_file, 'w') as f:
            json.dump(experiment_results, f, indent=2)
        
        print(f"\n💾 实验结果已保存: {results_file}")
        print("=" * 60)
        print("🎯 RGB+D真实数据实验完成！")
        print("📊 可与RGB baseline进行详细对比分析")
        print("🔄 建议下一步尝试更复杂的融合策略")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ RGB+D训练失败: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 故障排除建议:")
        print("1. 检查GPU内存是否足够 (4通道需要更多内存)")
        print("2. 尝试减小batch_size")
        print("3. 确认数据集路径配置正确")
        print("4. 检查深度图加载是否正常")

if __name__ == "__main__":
    main()
