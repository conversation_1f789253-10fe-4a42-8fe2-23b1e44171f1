#!/usr/bin/env python3
"""
RGB+D模型配置测试脚本
验证模型架构是否正确加载
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from ultralytics import YOLO
import traceback

def test_model_config(config_path, model_name):
    """测试模型配置"""
    print(f"\n🧪 测试模型: {model_name}")
    print(f"📋 配置文件: {config_path}")
    print("-" * 50)
    
    try:
        # 加载模型
        model = YOLO(config_path)
        print("✅ 模型配置加载成功")
        
        # 打印模型信息
        print(f"📊 模型参数量: {sum(p.numel() for p in model.model.parameters()):,}")
        print(f"🔧 模型层数: {len(list(model.model.modules()))}")
        
        # 测试前向传播
        print("\n🔍 测试前向传播...")
        
        # 创建测试输入
        if "rgbd" in config_path.lower():
            # RGB+D输入 (4通道)
            test_input = torch.randn(1, 4, 640, 640)
            print(f"📥 输入形状: {test_input.shape} (RGB+D)")
        else:
            # RGB输入 (3通道)
            test_input = torch.randn(1, 3, 640, 640)
            print(f"📥 输入形状: {test_input.shape} (RGB)")
        
        # 设置为评估模式
        model.model.eval()
        
        with torch.no_grad():
            output = model.model(test_input)

            # 调试输出结构
            print(f"🔍 输出类型: {type(output)}")
            print(f"🔍 输出长度: {len(output) if hasattr(output, '__len__') else 'N/A'}")

            # 安全地处理输出
            try:
                if isinstance(output, torch.Tensor):
                    print(f"📤 输出形状: {output.shape}")
                elif isinstance(output, (list, tuple)):
                    print(f"📤 输出是列表/元组，长度: {len(output)}")
                    for i, item in enumerate(output):
                        if isinstance(item, torch.Tensor):
                            print(f"    项{i}: Tensor {item.shape}")
                        elif isinstance(item, (list, tuple)):
                            print(f"    项{i}: 列表/元组，长度: {len(item)}")
                            for j, subitem in enumerate(item):
                                if isinstance(subitem, torch.Tensor):
                                    print(f"      子项{j}: Tensor {subitem.shape}")
                                else:
                                    print(f"      子项{j}: {type(subitem)}")
                        else:
                            print(f"    项{i}: {type(item)}")
                else:
                    print(f"📤 未知输出类型: {type(output)}")

                print("✅ 前向传播测试成功")

            except Exception as e:
                print(f"❌ 输出解析失败: {e}")
                print(f"原始输出: {output}")
                raise
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("🧪 RGB+D模型架构测试")
    print("=" * 60)
    
    # 测试模型列表
    test_models = [
        {
            'config': 'ultralytics/cfg/models/v12/yolov12m-rgbd-simple.yaml',
            'name': 'YOLOv12m RGB+D Simple (4-channel)'
        },
        {
            'config': 'ultralytics/cfg/models/v12/yolov12m-rgbd-fusion.yaml', 
            'name': 'YOLOv12m RGB+D Fusion (dual-stream)'
        }
    ]
    
    results = {}
    
    for model_info in test_models:
        config_path = model_info['config']
        model_name = model_info['name']
        
        # 检查配置文件是否存在
        if not os.path.exists(config_path):
            print(f"\n❌ 配置文件不存在: {config_path}")
            results[model_name] = False
            continue
        
        # 测试模型
        success = test_model_config(config_path, model_name)
        results[model_name] = success
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for model_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{model_name}: {status}")
    
    # 推荐下一步
    successful_models = [name for name, success in results.items() if success]
    
    if successful_models:
        print(f"\n🚀 推荐训练模型:")
        for model in successful_models:
            print(f"  • {model}")
        
        print(f"\n📝 下一步操作:")
        print("1. 准备RGB+D数据集")
        print("2. 运行训练脚本:")
        if "Simple" in successful_models[0]:
            print("   python scripts/train_rgbd_simple.py")
        else:
            print("   python scripts/train_rgbd_fusion.py")
    else:
        print(f"\n🔧 需要修复模型配置问题")
        print("建议检查:")
        print("1. 模块导入是否正确")
        print("2. 配置文件语法是否正确")
        print("3. 自定义模块是否正确注册")

if __name__ == "__main__":
    main()
